"""
Test client for FastAPI chat service
Demonstrates how to use both streaming and non-streaming endpoints
"""

import asyncio
import json
import aiohttp
import requests
from typing import List, Dict, Any


class ChatClient:
    """Client for interacting with the FastAPI chat service"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
    
    def chat_sync(self, messages: List[Dict[str, str]], model_name: str, 
                  model_type: str, jwt_token: str = None, mcp_url_list: List[str] = None,
                  team_id: int = None) -> Dict[str, Any]:
        """
        Synchronous non-streaming chat request
        
        Args:
            messages: List of message dictionaries
            model_name: Name of the model
            model_type: Type of model ('ollama' or 'openai')
            jwt_token: JWT token for authentication
            mcp_url_list: List of MCP server URLs
            team_id: Team ID for team-based chat
            
        Returns:
            Response dictionary
        """
        payload = {
            "messages": messages,
            "model_name": model_name,
            "model_type": model_type,
            "stream": False,
            "mcp_url_list": mcp_url_list or [],
            "team_id": team_id
        }
        
        if jwt_token:
            payload["jwt_token"] = jwt_token
        
        response = requests.post(f"{self.base_url}/chat", json=payload)
        response.raise_for_status()
        return response.json()
    
    async def chat_stream(self, messages: List[Dict[str, str]], model_name: str,
                         model_type: str, jwt_token: str = None, mcp_url_list: List[str] = None,
                         team_id: int = None):
        """
        Asynchronous streaming chat request
        
        Args:
            messages: List of message dictionaries
            model_name: Name of the model
            model_type: Type of model ('ollama' or 'openai')
            jwt_token: JWT token for authentication
            mcp_url_list: List of MCP server URLs
            team_id: Team ID for team-based chat
            
        Yields:
            Response chunks as they arrive
        """
        payload = {
            "messages": messages,
            "model_name": model_name,
            "model_type": model_type,
            "stream": True,
            "mcp_url_list": mcp_url_list or [],
            "team_id": team_id
        }
        
        if jwt_token:
            payload["jwt_token"] = jwt_token
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{self.base_url}/chat/stream", json=payload) as response:
                response.raise_for_status()
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if line.startswith('data: '):
                        data_str = line[6:]  # Remove 'data: ' prefix
                        try:
                            data = json.loads(data_str)
                            if data.get('type') == 'end':
                                break
                            yield data
                        except json.JSONDecodeError:
                            continue


async def test_ollama_chat():
    """Test Ollama model chat"""
    client = ChatClient()
    
    messages = [
        {"type": "text", "content": "Hello, how are you?", "role": "user"}
    ]
    
    print("Testing Ollama non-streaming chat...")
    try:
        result = client.chat_sync(
            messages=messages,
            model_name="qwen3:latest",
            model_type="ollama"
        )
        print("Response:", result)
    except Exception as e:
        print(f"Error: {e}")
    
    print("\nTesting Ollama streaming chat...")
    try:
        async for chunk in client.chat_stream(
            messages=messages,
            model_name="qwen3:latest",
            model_type="ollama"
        ):
            print("Chunk:", chunk)
    except Exception as e:
        print(f"Error: {e}")


async def test_openai_chat():
    """Test OpenAI model chat with JWT token"""
    client = ChatClient()
    
    # You would need to create a proper JWT token with your OpenAI credentials
    # This is just an example structure
    import jwt
    
    payload = {
        "openai_api_key": "your-openai-api-key",
        "openai_base_url": "https://api.openai.com/v1"
    }
    jwt_token = jwt.encode(payload, "xingchen-chatbot-secret-key-2025", algorithm="HS256")
    
    messages = [
        {"type": "text", "content": "What is the capital of France?", "role": "user"}
    ]
    
    print("Testing OpenAI non-streaming chat...")
    try:
        result = client.chat_sync(
            messages=messages,
            model_name="gpt-3.5-turbo",
            model_type="openai",
            jwt_token=jwt_token
        )
        print("Response:", result)
    except Exception as e:
        print(f"Error: {e}")


async def test_team_chat():
    """Test team-based chat"""
    client = ChatClient()
    
    # You would need to create a proper JWT token with your OpenAI credentials
    import jwt
    
    payload = {
        "openai_api_key": "your-openai-api-key",
        "openai_base_url": "https://api.openai.com/v1"
    }
    jwt_token = jwt.encode(payload, "xingchen-chatbot-secret-key-2025", algorithm="HS256")
    
    messages = [
        {"type": "text", "content": "Write a short poem about spring", "role": "user"}
    ]
    
    print("Testing team-based streaming chat...")
    try:
        async for chunk in client.chat_stream(
            messages=messages,
            model_name="",  # Not used for team chat
            model_type="",  # Not used for team chat
            jwt_token=jwt_token,
            team_id=6  # Use team ID from database
        ):
            print("Team response:", chunk)
    except Exception as e:
        print(f"Error: {e}")


async def test_health_check():
    """Test health check endpoint"""
    try:
        response = requests.get("http://localhost:8000/health")
        response.raise_for_status()
        print("Health check:", response.json())
        
        response = requests.get("http://localhost:8000/")
        response.raise_for_status()
        print("Root endpoint:", response.json())
    except Exception as e:
        print(f"Health check error: {e}")


async def main():
    """Main test function"""
    print("=== FastAPI Chat Service Test Client ===\n")
    
    # Test health check first
    await test_health_check()
    print()
    
    # Test different chat modes
    await test_ollama_chat()
    print()
    
    # Uncomment to test OpenAI (requires valid credentials)
    # await test_openai_chat()
    # print()
    
    # Uncomment to test team chat (requires valid team and credentials)
    # await test_team_chat()


if __name__ == "__main__":
    asyncio.run(main())
